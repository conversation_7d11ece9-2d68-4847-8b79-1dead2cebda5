'use server';

import { getCookiesAsString } from '@/actions/cookieAction';
import { redirectPath } from '@/actions/redirectPath';
import { routePaths } from '@/config';
import { HttpStatusCode } from '@/constants/api';
import https from 'https';

interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  status: number;
  message: string;
}

interface FetchConfig {
  options?: RequestInit;
  maxRetries?: number;
  retryDelay?: (attempt: number) => number;
}

function createHeaders(cookie: string, customHeaders?: HeadersInit): HeadersInit {
  return {
    Cookie: cookie,
    ...customHeaders,
  };
}

async function parseResponse<T>(res: Response): Promise<ApiResponse<T>> {
  const data = await res.json();
  return { success: true, data, status: res.status, message: res.statusText };
}

export async function serverFetcher<T>(url: string, config: FetchConfig = {}): Promise<ApiResponse<T>> {
  const { options } = config;

  const cookie = await getCookiesAsString();

  const agent = process.env.NODE_ENV === 'development' ? new https.Agent({ rejectUnauthorized: false }) : undefined;

  try {
    const res = await fetch(url, {
      ...options,
      headers: createHeaders(cookie, options?.headers),
      ...(agent && { agent }),
      credentials: 'include',
    });

    if (res.ok) {
      return await parseResponse<T>(res);
    }

    throw res;
  } catch (error) {
    const res = error as Response;
    console.error('fetcher error: ', { url: res.url, status: res.status, statusText: res.statusText });

    if (res.status === HttpStatusCode.UNAUTHORIZED) {
      await redirectPath(routePaths.login);
    }

    return {
      success: false,
      data: null,
      status: res.status,
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
