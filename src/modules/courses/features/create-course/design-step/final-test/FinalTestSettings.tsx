import { Select, Typography } from '@/components/ui';
import { Option } from '@/components/ui/select/select';
import { cn } from '@/lib/utils';
import LessonSettingsLayout from '@/modules/courses/features/create-course/design-step/components/SettingsLayout';
import { ClockIcon } from '@heroicons/react/24/outline';
import React from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { useTypedFormContext } from './useTypedFormContext';

function FinalTestSettings() {
  const { control, setValue } = useTypedFormContext();
  const questionsWatched = useWatch({ control, name: 'questions' });
  const minCorrectAnswerWatched = useWatch({ control, name: 'minCorrectAnswer' });

  const prevTotalRef = React.useRef<number>(0);

  const totalQuestions = questionsWatched?.length || 0;

  const getPassingScoreOptions = () => {
    if (totalQuestions === 0) return [];
    return [0.5, 0.6, 0.7, 0.8, 0.9, 1].map((p) => ({
      label: `${p * 100}%`,
      value: Math.ceil(totalQuestions * p),
      percent: p,
    }));
  };

  const getPassingScoreDescription = () => {
    if (totalQuestions === 0) return '';
    const minCorrect = minCorrectAnswerWatched || totalQuestions;
    return `Người học cần trả lời đúng ${minCorrect}/${totalQuestions} câu hỏi để vượt qua bài kiểm tra`;
  };

  React.useEffect(() => {
    if (totalQuestions <= 0) {
      prevTotalRef.current = totalQuestions;
      return;
    }

    const prevTotal = prevTotalRef.current || totalQuestions;

    const baseMin = minCorrectAnswerWatched ?? prevTotal;

    const percent = Math.min(1, Math.max(0, baseMin / Math.max(prevTotal, 1)));

    const newValue = Math.ceil(totalQuestions * percent);

    if (newValue !== (minCorrectAnswerWatched ?? 0)) {
      setValue('minCorrectAnswer', newValue);
    }

    prevTotalRef.current = totalQuestions;
  }, [totalQuestions, minCorrectAnswerWatched]);

  return (
    <LessonSettingsLayout>
      <div className="flex flex-col gap-8 p-4">
        <div className="flex justify-between">
          <Typography variant="labelMd">Tổng số câu hỏi (tối đa 50 câu hỏi):</Typography>
          <Typography variant="labelMd">{totalQuestions}</Typography>
        </div>

        <div className="flex flex-col gap-2">
          <div className="flex gap-0.5">
            <Typography variant="labelMd">Giới hạn thời gian làm bài tập</Typography>
            <div className="text-red-500">*</div>
          </div>

          <div
            className={cn(
              'flex w-full items-center justify-between',
              'rounded-lg border border-neutral-200 bg-neutral-100 px-4 py-3',
            )}
          >
            <Typography className="text-disabled_text">Không giới hạn</Typography>
            <ClockIcon className="size-6 text-disabled_text" />
          </div>
        </div>

        {totalQuestions > 0 && (
          <div className="flex flex-col gap-2">
            <div className="flex gap-0.5">
              <Typography variant="labelMd">Số câu đúng tối thiểu</Typography>
              <div className="text-red-500">*</div>
            </div>

            <Controller
              name="minCorrectAnswer"
              control={control}
              render={({ field }) => (
                <Select value={field.value || totalQuestions} onChange={field.onChange} disabled={totalQuestions < 10}>
                  {getPassingScoreOptions().map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              )}
            />

            <Typography variant="bodySm" className="text-secondary_text">
              {getPassingScoreDescription()}
            </Typography>
          </div>
        )}
      </div>
    </LessonSettingsLayout>
  );
}

export default FinalTestSettings;
