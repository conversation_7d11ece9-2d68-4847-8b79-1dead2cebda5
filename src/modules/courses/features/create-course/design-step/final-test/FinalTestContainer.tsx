'use client';

import { useNotification } from '@/hooks';
import useTestActions from '@/modules/courses/features/create-course/design-step/hooks/useTestActions';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FinalTestFormData } from './final-test-form.type';
import { finalTestSchema, getFinalTestDefaultValues, getFinalTestPayloadRequest } from './final-test.schema';
import QuizHeader from './FinalTestHeader';
import QuizSettings from './FinalTestSettings';
import QuestionForm from './QuestionForm';

const useFinalTestForm = () => {
  const notification = useNotification();
  const { onUpdateTest, testDetail } = useTestActions();

  const formMethods = useForm({ mode: 'all', resolver: zodResolver(finalTestSchema) });

  const handleSaveTest = (values: FinalTestFormData) => {
    if (!testDetail) return;

    const payload = getFinalTestPayloadRequest(values, testDetail);
    onUpdateTest(payload, {
      onSuccess: () => {
        notification.success({ message: 'Lưu bài kiểm tra thành công' });
      },
      onError: () => {
        notification.error({ message: 'Lưu bài kiểm tra thất bại' });
      },
    });
    formMethods.reset(values);
  };

  React.useEffect(() => {
    if (testDetail) {
      const defaultValues = getFinalTestDefaultValues(testDetail);
      formMethods.reset(defaultValues);
    }
  }, [testDetail]);

  return {
    formMethods,
    onSaveTest: handleSaveTest,
  };
};

export default function FinalTestContainer() {
  const { formMethods, onSaveTest } = useFinalTestForm();

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSaveTest)} className="h-full">
        <div className="flex h-full flex-col gap-4 overflow-y-auto px-8 py-10">
          <QuizHeader />

          <div className="flex h-full min-h-fit gap-6">
            <QuestionForm />
            <QuizSettings />
          </div>
        </div>
      </form>
    </FormProvider>
  );
}
