import {
  FinalTestFormData,
  FinalTestQuestionFormData,
} from '@/modules/courses/features/create-course/design-step/final-test/final-test-form.type';
import { QuestionPayloadRequest, TestPayloadRequest } from '@/modules/courses/services/test.service';
import { Question, Test } from '@/modules/courses/types/test.type';
import * as zod from 'zod';

export const fileSchema = zod
  .object({
    fileUrl: zod.string(),
    fileId: zod.string(),
  })
  .optional()
  .nullable();

export const finalTestSchema = zod
  .object({
    questions: zod.array(
      zod.object({
        id: zod.string(),
        isMultipleChoice: zod.boolean(),
        questionName: zod
          .string()
          .min(1, 'Câu hỏi không được để trống')
          .max(200, 'Tên câu hỏi vượt quá giới hạn cho phép. Độ dài tối đa là 200 ký tự.'),
        questionThumbnailImage: fileSchema,
        questionVideoUrl: fileSchema,
        questionCorrectAnswer: zod.array(zod.number()).min(1, 'Đáp án không được để trống'),
        questionAnswers: zod
          .array(
            zod.object({
              answerIndex: zod.number(),
              answerName: zod
                .string()
                .min(1, 'Tên đáp án không được để trống')
                .refine((value) => {
                  const HTML_TAG_REGEX = /<[^>]*>/g;
                  const content = value.replace(HTML_TAG_REGEX, '').trim();
                  return content.length;
                }, 'Đáp án không được để trống')
                .max(200, 'Tên đáp án vượt quá giới hạn cho phép. Độ dài tối đa là 200 ký tự.'),
              answerThumbnailImage: fileSchema,
            }),
          )
          .min(2, 'Phải có ít nhất 2 đáp án'),
      }),
    ),
    minQuestions: zod.number().optional(),
    maxQuestions: zod.number().optional(),
    minCorrectAnswer: zod.number().positive(),
  })
  .refine(
    (data) => {
      if (!data.minCorrectAnswer || !data.questions || !Array.isArray(data.questions)) {
        return true;
      }

      const totalQuestions = data.questions.length;
      if (totalQuestions === 0) return true;

      const minAllowed = Math.ceil(totalQuestions * 0.5);
      const maxAllowed = totalQuestions;

      return data.minCorrectAnswer >= minAllowed && data.minCorrectAnswer <= maxAllowed;
    },
    {
      message: 'Số câu đúng tối thiểu phải từ 50% đến 100% tổng số câu hỏi',
      path: ['minCorrectAnswer'],
    },
  );

export const mapQuestionToFormData = (question: Question): FinalTestQuestionFormData => ({
  id: question.id,
  isMultipleChoice: question.questionTypeId === 1,
  questionName: question.questionName ?? '',
  questionThumbnailImage: question.questionImageFile && {
    fileId: question.questionImageFile.id,
    fileUrl: question.questionImageFile.fileUrl,
  },
  questionVideoUrl: question.videoFile && {
    fileId: question.videoFile.id,
    fileUrl: question.videoFile.fileUrl,
  },
  questionCorrectAnswer: question.correctAnswer ?? [],
  questionAnswers: question.questionOptions.map((option) => ({
    answerIndex: option.optionIndex,
    answerName: option.optionName ?? '',
    answerThumbnailImage: option.optionThumbnailImageFile && {
      fileId: option.optionThumbnailImageFile.id,
      fileUrl: option.optionThumbnailImageFile.fileUrl,
    },
  })),
});

export const getFinalTestDefaultValues = (testDetail: Test) => {
  if (!testDetail) {
    return {
      questions: [],
      minQuestions: 1,
      maxQuestions: 50,
      minCorrectAnswer: 10,
    } satisfies FinalTestFormData;
  }

  const totalQuestions = testDetail.questions.length;
  const defaultMinCorrect = totalQuestions > 0 ? totalQuestions : 10;

  return {
    questions: testDetail.questions.map(mapQuestionToFormData),
    minQuestions: 1,
    maxQuestions: 50,
    minCorrectAnswer: testDetail.minCorrectAnswer || defaultMinCorrect,
  } satisfies FinalTestFormData;
};

export const mapQuestionToPayload = (question: FinalTestQuestionFormData, index: number): QuestionPayloadRequest => ({
  question_name: question.questionName ?? '',
  question_image_file: question.questionThumbnailImage?.fileId ?? '',
  video_file: question.questionVideoUrl?.fileId,
  question_options: question.questionAnswers.map((answer, idx) => ({
    option_index: idx,
    option_name: answer.answerName ?? '',
    option_thumbnail_image_file: answer.answerThumbnailImage?.fileId ?? '',
  })),
  correct_answer: question.questionCorrectAnswer.map(Number),
  sort_index: index + 1,
});

export const getFinalTestPayloadRequest = (values: FinalTestFormData, testDetail: Test) => {
  return {
    test_type: 'FINAL_TEST',
    content: {
      test_name: testDetail.testName,
      has_limit_time: 0,
      limit_time: 0,
      questions: values.questions.map(mapQuestionToPayload),
      min_correct_answer: values.minCorrectAnswer,
    },
  } satisfies TestPayloadRequest;
};
