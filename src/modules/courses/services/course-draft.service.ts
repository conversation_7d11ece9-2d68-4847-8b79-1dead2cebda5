import { API_ENDPOINTS, HttpMethod } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { SearchParamsProps } from '@/type/appProps';

import { formatApiUrl } from '@/utils/url.util';
import queryString from 'query-string';

class CourseDraftService {
  static async createCourseDraftService(payload: { courseId: string }) {
    const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.COURSE_DRAFT, { courseId: payload.courseId });
    const response = await fetcher<{ id: string }>(url, { method: HttpMethod.POST });
    return response.data;
  }

  static async confirmCourseDraftService(payload: { courseId: string; state: 'CONFIRMED' | 'REJECTED' }) {
    const url = queryString.stringifyUrl({
      url: formatApiUrl(API_ENDPOINTS.COURSES.PATCH.CONFIRM_COURSE_DRAFT, { courseId: payload.courseId }),
      query: { state: payload.state },
    });
    const response = await fetcher<{ id: string }>(url, { method: HttpMethod.PATCH });
    return response.data;
  }

  static getConfirmationState(searchParams: SearchParamsProps) {
    const { edit_more, proceed } = searchParams;
    const isEditMore = edit_more === 'true';
    const isProceed = proceed === 'true';
    if (isEditMore) return 'REJECTED';
    if (isProceed) return 'CONFIRMED';
    return null;
  }

  static async getCourseDraft({ courseId, course }: { courseId: string; course: CourseInfo | null }) {
    const isPublished = Boolean(course?.publish);
    const isApproved = Boolean(course?.active);
    const shouldCreateDraft = isPublished && isApproved;
    const courseDraft = shouldCreateDraft ? await this.createCourseDraftService({ courseId }) : null;

    return courseDraft;
  }

  static async handleConfirmDraftChanges({
    courseId,
    searchParams,
  }: {
    courseId: string;
    searchParams: SearchParamsProps;
  }) {
    try {
      const confirmState = this.getConfirmationState(searchParams);
      if (!confirmState) return;
      await this.confirmCourseDraftService({ courseId, state: confirmState });
    } catch (error) {
      console.error('Error confirming course draft:', error);
    }
  }
}

export default CourseDraftService;
