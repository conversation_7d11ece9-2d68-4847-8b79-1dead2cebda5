'use server';

import { routePaths } from '@/config';
import { userServices } from '@/services/user.services';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';

export async function checkAuthAndRedirect() {
  const { isLoggedIn } = await userServices();

  if (!isLoggedIn) {
    redirect(routePaths.login);
  }

  return { isLoggedIn };
}

export async function getNextHeaders() {
  const headersList = await headers();
  return headersList;
}
