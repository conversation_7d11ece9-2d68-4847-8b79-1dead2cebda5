const BASE_API_URL_V1 = process.env.NEXT_PUBLIC_BASE_API_URL_V1;

export const getApiUrlV1 = (endpoint: string): string => `${BASE_API_URL_V1}${endpoint}`;

const API_ROOT_ENDPOINTS = {
  USERS: getApiUrlV1('/users'),
  COURSES: getApiUrlV1('/courses'),
  COURSE_PUBLIC: getApiUrlV1('/public/courses'),
  USER_PUBLIC: getApiUrlV1('/public/users'),
};

const USER_FILE = `${API_ROOT_ENDPOINTS.USERS}/files`;

export const API_ENDPOINTS = {
  USERS: {
    GET: {
      GOOGLE_LOGIN: `${API_ROOT_ENDPOINTS.USERS}/google/login`,
      GOOGLE_LOGIN_URL_CALLBACK: `${API_ROOT_ENDPOINTS.USERS}/google/callback`,
      OUTSTANDING_CREATORS: `${API_ROOT_ENDPOINTS.USERS}/outstanding-authors`,
      FILES: USER_FILE,
      CREATOR_AUTH: `${API_ROOT_ENDPOINTS.USERS}/creator/:userId/`,

      //PUBLIC
      CREATOR_PUBLIC: `${API_ROOT_ENDPOINTS.USER_PUBLIC}/creator/:userId`,
    },
    POST: {
      LOGIN: `${API_ROOT_ENDPOINTS.USERS}/login-by-email`,
      LOGOUT: `${API_ROOT_ENDPOINTS.USERS}/logout`,
      REGISTER: `${API_ROOT_ENDPOINTS.USERS}`,
      RESET_PASSWORD: `${API_ROOT_ENDPOINTS.USERS}/reset-password`,
      CONFIRM_EMAIL: `${API_ROOT_ENDPOINTS.USERS}/confirm`,
      RESEND_EMAIL: `${getApiUrlV1('/resend-email')}`,
      UPDATE_PROFILE: `${API_ROOT_ENDPOINTS.USERS}/profile`,
      UPLOAD_FILE: USER_FILE,
      UPLOAD_FILE_IN_CHUNK: `${USER_FILE}/chunk`,
      UPLOAD_FILE_IN_MULTIPART: `${USER_FILE}/multipart-upload`,
      UPLOAD_FILE_IN_MULTIPART_COMPLETE: `${USER_FILE}/multipart-upload/complete`,
    },
    PUT: {
      CONFIRM_PASSWORD: `${API_ROOT_ENDPOINTS.USERS}/reset-password`,
      UPDATE_FILE: USER_FILE,
    },
    DELETE: {
      DELETE_FILE: `${USER_FILE}/:id`,
    },
    PATCH: {
      RENAME_FILE: `${USER_FILE}/:id`,
    },
  },

  COURSES: {
    GET: {
      // PUBLIC
      OUTSTANDING_COURSES_PUBLIC: `${API_ROOT_ENDPOINTS.COURSE_PUBLIC}/outstanding-courses`,
      SEARCH_COURSES_PUBLIC: `${API_ROOT_ENDPOINTS.COURSE_PUBLIC}/search-courses`,
      COURSES_PUBLIC: `${API_ROOT_ENDPOINTS.COURSE_PUBLIC}`,
      TOPICS_PUBLIC: `${API_ROOT_ENDPOINTS.COURSE_PUBLIC}/topics`,
      COURSE_DETAIL_PUBLIC: `${API_ROOT_ENDPOINTS.COURSE_PUBLIC}/:courseId`,
      COURSES_BY_CREATOR_PUBLIC: `${API_ROOT_ENDPOINTS.COURSE_PUBLIC}/creator/:userId`,

      //PRIVATE

      COURSES: `${API_ROOT_ENDPOINTS.COURSES}`,
      USER_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/user-course`,
      NEW_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/new-courses`,
      OUTSTANDING_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/outstanding-courses`,
      SEARCH_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/search-courses`,
      COURSES_BY_CREATOR: `${API_ROOT_ENDPOINTS.COURSES}/creator/:userId`,
      COURSE_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId`,
      TAGS: getApiUrlV1('/tags'),
      ALL_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/auth`,
      LECTURE_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      FAVORITE_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/favorites`,
      FAVORITE_CREATORS: `${API_ROOT_ENDPOINTS.COURSES}/authors/favorites`,
      SECTION_TEST_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId`,
      LEANER_TEST_ANSWER: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId/answers`,
      METRICS_SUMMARY: `${API_ROOT_ENDPOINTS.COURSES}/metrics/summary`,
      METRICS_DETAILED: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/metrics/detailed`,

      // revamp
      TEST_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId`,
    },
    POST: {
      CREATE_COURSE: `${API_ROOT_ENDPOINTS.COURSES}`,
      CREATE_SECTION: `${API_ROOT_ENDPOINTS.COURSES}/sections`,
      DELETE_SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId`,
      CREATE_TAG: getApiUrlV1('/tags'),
      UPLOAD: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/upload`,
      CREATE_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures`,
      CREATE_SLIDE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/slide`,
      CREATE_SLIDE_ITEM: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/slide/:slideId`,
      FAVORITE_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/favorite`,
      FAVORITE_CREATOR: `${API_ROOT_ENDPOINTS.COURSES}/authors/:authorId/favorite`,
      CREATE_QUESTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/questions`,
      CREATE_QUESTION_TEST: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests`,
      PROCESS_LEARNER_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      LEANER_TEST_ANSWER: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId/answers`,
      COURSE_REVIEW: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/reviews`,
      CREATE_LECTURE_TEST: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/tests`,

      //revamp
      SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections`,
      LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures`,
      INTERACTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/interactions`,
      CREATE_FAQ: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/faqs`,
      COURSE_DRAFT: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/drafts`,
    },
    PUT: {
      EDIT_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId`,
      EDIT_SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId`,
      EDIT_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,

      //revamp
      SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId`,
      LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      INTERACTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/interactions/:interactId/content`,
      INTERACTION_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/interactions/:interactId`,
      EDIT_FAQ: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/faqs/:faqId`,
      TEST: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId`,
    },
    PATCH: {
      PUBLISH_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/publish/:courseId`,
      UPDATE_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      SWAP_SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/swap/positions`,
      SWAP_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/swap/positions`,

      LECTURE_VIDEO: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/video`,
      INTERACTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/interactions/:interactId`,
      SWAP_FAQ: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/faqs/swap/positions`,

      //revamp
      CONFIRM_COURSE_DRAFT: `${API_ROOT_ENDPOINTS.COURSES}/publish/:courseId/confirm`,
    },
    DELETE: {
      DELETE_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      DELETE_SLIDE: `${API_ROOT_ENDPOINTS.COURSES}/lectures/:lectureId/slide/:slideId/slide-items/:slideItemId`,
      DELETE_VIDEO_INTERACTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/interactions/:interactId`,
      DELETE_QUESTION_TEST: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId/questions/:questionId`,
      DELETE_LECTURE_TEST: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/tests/:testId`,

      //revamp
      SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId`,
      LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      INTERACTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/interactions/:interactId`,
      DELETE_FAQ: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/faqs/:faqId`,
    },
  },
};

export const NEXT_API_ENDPOINTS = {
  AUTH: {
    LOGOUT: '/api/auth/logout',
  },
};

export enum HttpStatusCode {
  SUCCESS = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}
