'use client';

import { useCourseMetricsDetailed } from '@/hooks/apis/course/useCourseMetrics';
import { useCourseInfo } from '@/hooks/useCourseInfo';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import { Select } from 'antd';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { routePaths } from '../../../../../../config';

interface MetricsPageProps {
  courseId: string;
}

interface DateRange {
  fromDate: string;
  toDate: string;
  label: string;
  value: string;
}

function MetricsPage({ courseId }: MetricsPageProps) {
  // Generate 5 recent months with proper date ranges
  const monthOptions = useMemo(() => {
    const options: DateRange[] = [];
    const today = new Date();

    for (let i = 0; i < 5; i++) {
      const currentMonth = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthNames = [
        'Tháng 1',
        'Tháng 2',
        'Tháng 3',
        'Tháng 4',
        'Tháng 5',
        'Tháng 6',
        'Tháng 7',
        'Tháng 8',
        'Tháng 9',
        'Tháng 10',
        'Tháng 11',
        'Tháng 12',
      ];

      let fromDate: Date;
      let toDate: Date;
      let label: string;

      if (i === 0) {
        // Current month: from 1st to today
        fromDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        toDate = today;
        const fromDateStr = `${fromDate.getDate().toString().padStart(2, '0')}/${(fromDate.getMonth() + 1).toString().padStart(2, '0')}/${fromDate.getFullYear()}`;
        const todayStr = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
        label = `${monthNames[currentMonth.getMonth()]} (${fromDateStr} - nay)`;
      } else {
        // Previous months: full month
        fromDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        toDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0); // Last day of month
        const fromDateStr = `${fromDate.getDate().toString().padStart(2, '0')}/${(fromDate.getMonth() + 1).toString().padStart(2, '0')}/${fromDate.getFullYear()}`;
        const toDateStr = `${toDate.getDate().toString().padStart(2, '0')}/${(toDate.getMonth() + 1).toString().padStart(2, '0')}/${toDate.getFullYear()}`;
        label = `${monthNames[currentMonth.getMonth()]} (${fromDateStr} - ${toDateStr})`;
      }

      const fromDateISO = fromDate.toISOString().split('T')[0];
      const toDateISO = toDate.toISOString().split('T')[0];

      options.push({
        fromDate: fromDateISO,
        toDate: toDateISO,
        label,
        value: fromDateISO + '_' + toDateISO,
      });
    }

    return options;
  }, []);

  const [selectedDateRange, setSelectedDateRange] = useState<string>(monthOptions[0].value);
  const router = useRouter();
  const currentRange = monthOptions.find((option) => option.value === selectedDateRange) || monthOptions[0];

  const { courseInfo } = useCourseInfo(courseId);

  const {
    data: metrics,
    isLoading,
    error,
  } = useCourseMetricsDetailed(courseId, currentRange.fromDate, currentRange.toDate);
  console.log('courseInfo', courseInfo);
  return (
    <div className="bg-gray-50 min-h-screen p-6">
      {/* Breadcrumb Navigation */}
      <div className="mb-6">
        <div className="mb-6 flex items-center">
          <div
            className="flex cursor-pointer items-center"
            onClick={() => router.push(routePaths.profile.children.creator.path)}
          >
            <ChevronLeftIcon width={20} height={20} />
            <span className="ml-2">Trở về Danh sách khóa học</span>
          </div>
        </div>

        <div className="mb-6">
          <h1 className="text-gray-900 mb-2 text-3xl font-bold">Hiệu suất khóa học</h1>
          <p className="text-gray-600">Khóa học: {courseInfo?.courseName || 'Đang tải...'}</p>
        </div>

        <div className="mb-8">
          <Select
            value={selectedDateRange}
            onChange={setSelectedDateRange}
            style={{ width: 400 }}
            options={monthOptions.map((option) => ({
              value: option.value,
              label: option.label,
            }))}
          />
        </div>
      </div>

      <div className="flex gap-10">
        <div className="flex-[3]">
          <div className="flex flex-col gap-5">
            <div className="h-[120px] rounded-lg border bg-white px-6 shadow-sm">
              <div className="flex h-full flex-col justify-center">
                <div className="text-gray-900 mb-2 text-4xl font-bold">{metrics?.totalEnrolled}</div>
                <div className="text-gray-600">Số lượt đăng ký</div>
              </div>
            </div>
            <div className="h-[120px] rounded-lg border bg-white p-6 shadow-sm">
              <div className="flex h-full flex-col justify-center">
                <div className="text-gray-900 mb-2 text-4xl font-bold">{metrics?.totalCompleted}</div>
                <div className="text-gray-600">Số lượt hoàn thành</div>
              </div>
            </div>
            <div className="h-[120px] rounded-lg border bg-white p-6 shadow-sm">
              <div className="flex h-full flex-col justify-center">
                <div className="mb-2 flex items-center">
                  <div className="text-gray-900 text-4xl font-bold">{metrics?.averageCompletionRate}%</div>
                  <div className="bg-gray-400 ml-2 flex h-4 w-4 items-center justify-center rounded-full">
                    <span className="text-xs text-white">?</span>
                  </div>
                </div>
                <div className="text-gray-600">Phần trăm thời lượng học trung bình</div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex-[7]">
          <div className="flex h-full flex-col gap-5">
            <div className="relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 p-6 text-white shadow-sm">
              <div className="relative z-10">
                <div className="mb-2 text-2xl font-bold">
                  {metrics?.revenueRange
                    ? `${metrics.revenueRange.min.toLocaleString()} - ${metrics.revenueRange.max.toLocaleString()}`
                    : '4,890,020 - 5,020,000'}
                </div>
                <div className="text-blue-100">Khoảng doanh thu dự kiến (VND)</div>
              </div>
              <div className="absolute -right-4 -top-4 h-24 w-24 opacity-20">
                <div className="h-full w-full rotate-12 transform rounded-full bg-yellow-400"></div>
              </div>
            </div>
            <div className="flex-1 rounded-lg border bg-white p-6 shadow-sm">
              <div className="mb-6 flex h-[104px] items-start justify-between">
                <h2 className="text-gray-900 text-xl font-semibold">3 Nội dung học được hoàn thành nhiều nhất</h2>
                <span className="text-gray-500">(Phần trăm hoàn thành)</span>
              </div>

              <div className="space-y-4">
                {metrics?.topLessons?.length &&
                  metrics.topLessons.map((lesson, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-gray-900">{lesson.name}</span>
                      <span className="text-gray-900 font-semibold">{lesson.completionRate}%</span>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MetricsPage;
