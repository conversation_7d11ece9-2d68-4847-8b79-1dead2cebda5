'use server';

import { routePaths } from '@/config';
import { COOKIE_NAMES } from '@/constants/auth';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://beta.studify.vn';

  if (!process.env.NEXT_PUBLIC_BASE_URL) {
    console.warn('NEXT_PUBLIC_BASE_URL is not set, using fallback URL');
  }

  const nextUrl = request.headers.get('next-url') || '/';
  const fullNextUrl = `${baseUrl}${nextUrl}`;
  const refererUrl = request.headers.get('referer');

  const redirectUrl = refererUrl || fullNextUrl;
  const loginUrl = `${baseUrl}${routePaths.login}?redirectUrl=${encodeURIComponent(redirectUrl)}`;

  const options = { maxAge: 0, domain: '.studify.vn', httpOnly: true, path: '/' };
  const response = NextResponse.redirect(loginUrl, { status: 302 });

  response.cookies.set(COOKIE_NAMES.ACCESS_TOKEN, '', options);
  response.cookies.set(COOKIE_NAMES.REFRESH_TOKEN, '', options);
  response.cookies.set(COOKIE_NAMES.USER_INFO, '', options);

  return response;
}
